#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模拟数据生成脚本
根据真实业务流程的时间关系，批量生成模拟订单数据
"""

import mysql.connector
import sys
import random
import time
from datetime import datetime, timedelta
from mysql.connector import errorcode
import uuid
import hashlib

# 数据库连接配置
DB_CONFIG = {
    'user': 'root',
    'password': 'BwYvW64msQ5Lndu2Umpn',
    'host': 'nexus-master.cl4kksw821u9.ap-east-1.rds.amazonaws.com',
    'port': 3306,
    'database': 'staging_card'
}

# 模拟数据配置
MOCK_CONFIG = {
    'batch_size': 1,  # 每批生成的订单数量（建议先用小数量测试）
    'time_interval_seconds': 1,  # 每个订单之间的时间间隔（秒）
    'seller_user_id': 121,  # 出卡用户ID
    'buyer_user_id': 120,   # 收卡用户ID
    'seller_company_id': 38,  # 出卡公司ID
    'buyer_company_id': 37,   # 收卡公司ID
    'card_id': 47,           # 卡片ID (Steam US)
    'country_id': 1,         # 国家ID (美国)
}

# 业务流程时间关系说明：
# 1. t1: 查询汇率 (基础时间)
# 2. t2: 上传图片 (t1 + 3秒)
# 3. t3: 创建订单 (t2 + 1秒)
# 4. t4: 访问页面 (t3 + 3秒)
# 5. t5: 系统分配 (t4 + 1秒)
# 6. t6: 收卡商接单 (t5 + 3秒)
# 7. t7: 完成订单 (t6 + 1秒)
# 总耗时约12秒完成一个完整订单流程

def create_db_connection():
    """创建数据库连接"""
    try:
        cnx = mysql.connector.connect(**DB_CONFIG)
        print("✅ 数据库连接成功")
        return cnx
    except mysql.connector.Error as err:
        if err.errno == errorcode.ER_ACCESS_DENIED_ERROR:
            print("❌ 数据库连接失败：用户名或密码错误")
        elif err.errno == errorcode.ER_BAD_DB_ERROR:
            print(f"❌ 数据库连接失败：数据库 '{DB_CONFIG['database']}' 不存在")
        else:
            print(f"❌ 数据库连接失败：{err}")
        sys.exit(1)

def generate_order_id():
    """生成订单ID（模拟真实格式）"""
    timestamp = int(time.time())
    random_part = ''.join(random.choices('0123456789abcdef', k=8))
    return f"25{str(timestamp)[-8:]}{random_part}"

def generate_extra_id():
    """生成订单卡片的额外ID"""
    return ''.join(random.choices('0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ', k=16))

def generate_image_path():
    """生成图片路径"""
    date_str = datetime.now().strftime('%Y%m%d')
    uuid_str = str(uuid.uuid4())
    return f"s3/ap-east/card-platform/private/staging/card-images/{date_str}/{uuid_str}.jpeg"

def generate_image_md5():
    """生成图片MD5"""
    return hashlib.md5(str(random.random()).encode()).hexdigest()

def insert_order_data(cursor, base_time, order_index):
    """
    插入一个完整订单的所有相关数据
    按照真实业务流程的时间顺序
    """
    print(f"\n🔄 正在生成第 {order_index + 1} 个订单...")
    
    # 生成基础数据
    order_id = generate_order_id()
    order_card_id = random.randint(7017072577, 9999999999)
    extra_id = generate_extra_id()
    image_path = generate_image_path()
    image_md5 = generate_image_md5()
    balance = random.choice([25, 50, 100, 200])
    rate = 1750.0
    rmb_rate = 7.0
    commission = balance * 0.7  # 70% 手续费
    profit = balance * rmb_rate - commission
    
    # 时间序列（模拟真实业务流程）
    t1 = base_time  # 查询汇率时间
    t2 = t1 + timedelta(seconds=3)   # 上传图片时间
    t3 = t2 + timedelta(seconds=1)   # 创建订单时间
    t4 = t3 + timedelta(seconds=3)   # 访问页面时间
    t5 = t4 + timedelta(seconds=1)   # 系统分配时间
    t6 = t5 + timedelta(seconds=3)   # 收卡商接单时间
    t7 = t6 + timedelta(seconds=1)   # 完成订单时间
    
    try:
        # 1. 插入 admin_operation_log - 查询汇率
        cursor.execute("""
            INSERT INTO admin_operation_log (user_id, path, method, ip, input, created_at, updated_at)
            VALUES (%s, %s, %s, %s, %s, %s, %s)
        """, (
            MOCK_CONFIG['seller_user_id'],
            'admin/company/seller/user/orders/search-card-rate',
            'GET',
            '127.0.0.1',
            f'{{"card_id":"{MOCK_CONFIG["card_id"]}","country_id":"{MOCK_CONFIG["country_id"]}","balance":"{balance}"}}',
            t1, t1
        ))
        
        # 2. 插入 admin_operation_log - 获取卡片汇率
        cursor.execute("""
            INSERT INTO admin_operation_log (user_id, path, method, ip, input, created_at, updated_at)
            VALUES (%s, %s, %s, %s, %s, %s, %s)
        """, (
            MOCK_CONFIG['seller_user_id'],
            'admin/company/seller/user/orders/get-card-rate',
            'GET',
            '127.0.0.1',
            f'{{"card_id":"{MOCK_CONFIG["card_id"]}","country_id":"{MOCK_CONFIG["country_id"]}","balance":"{balance}","card_type":"1","is_fast":"1"}}',
            t1, t1
        ))
        
        # 3. 插入 order_card_images - 上传图片
        image_id = random.randint(7906, 99999)
        cursor.execute("""
            INSERT INTO order_card_images (id, image, image_md5, ocr_info, order_card_id, created_at, updated_at, admin_user_id, is_receipt)
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
        """, (
            image_id, image_path, image_md5, '[]', order_card_id, t2, t2, MOCK_CONFIG['seller_user_id'], 0
        ))
        
        # 4. 插入 admin_operation_log - 上传图片
        cursor.execute("""
            INSERT INTO admin_operation_log (user_id, path, method, ip, input, created_at, updated_at)
            VALUES (%s, %s, %s, %s, %s, %s, %s)
        """, (
            MOCK_CONFIG['seller_user_id'],
            'admin/company/seller/user/orders/images/upload',
            'POST',
            '127.0.0.1',
            '{"is_receipt":"0"}',
            t2, t2
        ))
        
        # 5. 插入 orders - 创建订单
        cursor.execute("""
            INSERT INTO orders (id, company_id, owner_type, owner_id, status, withdrawal_status,
                               machine, operator_id, amount_profit_rmb, amount_performance_rmb,
                               commission, amount_total_income_rmb, amount_total_withdraw_rmb,
                               is_restock, is_image_link, user_source, created_at, updated_at,
                               completed_order_cards_balance, rmb_income, amount_profit_rate, agent_operator_id)
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        """, (
            order_id, MOCK_CONFIG['seller_company_id'], 'AdminUser', MOCK_CONFIG['seller_user_id'],
            2, 1, MOCK_CONFIG['seller_user_id'], MOCK_CONFIG['seller_user_id'],
            profit, balance * rmb_rate, commission, profit, 0.0, 0, 0, 0, t3, t7,
            balance, balance * rmb_rate, 90.0, MOCK_CONFIG['seller_user_id']
        ))

        # 6. 插入 order_statistics - 订单统计
        cursor.execute("""
            INSERT INTO order_statistics (order_id, card_count, amount_turnover_rmb, created_at, updated_at)
            VALUES (%s, %s, %s, %s, %s)
        """, (order_id, 1, balance * rmb_rate, t3, t7))

        # 7. 插入 admin_operation_log - 创建订单
        cursor.execute("""
            INSERT INTO admin_operation_log (user_id, path, method, ip, input, created_at, updated_at)
            VALUES (%s, %s, %s, %s, %s, %s, %s)
        """, (
            MOCK_CONFIG['seller_user_id'],
            'admin/company/seller/user/orders',
            'POST',
            '127.0.0.1',
            f'{{"cards":[{{"card_id":"{MOCK_CONFIG["card_id"]}","balance":"{balance}","card_info":null,"image_ids":["{image_id}"],"machine":"{MOCK_CONFIG["seller_user_id"]}","operator_id":"{MOCK_CONFIG["seller_user_id"]}","country_id":"{MOCK_CONFIG["country_id"]}","card_type":"1","is_fast":"1","rate":"{int(rate)}","rmb_rate":"{rmb_rate}","can_batch_sell":"0"}}]}}',
            t3, t3
        ))

        # 8. 插入 order_cards - 订单卡片
        cursor.execute("""
            INSERT INTO order_cards (id, extra_id, card_type, balance, rate_balance, rate, rmb_rate,
                                   commission, buyer_commission, final_rmb_rate, final_rate, status,
                                   is_auto_compete, order_id, card_id, country_id, is_fast,
                                   company_card_rates, machine, operator_id, batch, created_at, updated_at,
                                   buyer_company_id, checked_at, local_checked_at, completed_at,
                                   local_completed_at, buyer_id, processed_at, is_ocr_resolved,
                                   is_app_artificial_card, profit_rate, buyer_rmb_rate, is_auto_buy_failed,
                                   reward_amount, fission_divide_rmb_amount, is_app_order, user_confirm,
                                   user_confirmed_rate, is_rate_down, fission_commission_admin_user_rmb_amount,
                                   fission_commission_company_rmb_amount, user_country_id)
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        """, (
            order_card_id, extra_id, 1, balance, balance, rate, rmb_rate, commission, 0.0, rmb_rate, rate, 4, 0,
            order_id, MOCK_CONFIG['card_id'], MOCK_CONFIG['country_id'], 1,
            f'[{{"id":21892,"company_id":{MOCK_CONFIG["buyer_company_id"]},"rmb_rate":"{rmb_rate}","created_at":"2024-12-26T14:38:36.000000Z"}}]',
            MOCK_CONFIG['seller_user_id'], MOCK_CONFIG['seller_user_id'], 0, t3, t7,
            MOCK_CONFIG['buyer_company_id'], t6, t6, t7, t7, MOCK_CONFIG['buyer_user_id'], t7, 1, 0, 0.0, 0.0, 0,
            0.0, 0.0, 0, 0, 0.0, 0, 0.0, 0.0, MOCK_CONFIG['country_id']
        ))

        # 9. 插入 admin_operation_log - 访问创建页面
        cursor.execute("""
            INSERT INTO admin_operation_log (user_id, path, method, ip, input, created_at, updated_at)
            VALUES (%s, %s, %s, %s, %s, %s, %s)
        """, (
            MOCK_CONFIG['seller_user_id'],
            'admin/company/seller/user/orders/create',
            'GET',
            '127.0.0.1',
            '[]',
            t4, t4
        ))

        # 10. 插入 order_card_logs - 创建订单日志
        cursor.execute("""
            INSERT INTO order_card_logs (order_card_id, description, description_en, ip, created_at, updated_at)
            VALUES (%s, %s, %s, %s, %s, %s)
        """, (
            order_card_id,
            f'[子拓出卡管理] 成功创建订单[t-Steam-US美国-{balance}*1]',
            f'[子拓出卡管理] successfully created the order[t-Steam-US美国-{balance}*1]',
            '127.0.0.1',
            t3, t3
        ))

        # 11. 插入 order_card_logs - 系统分配日志
        cursor.execute("""
            INSERT INTO order_card_logs (order_card_id, description, description_en, ip, created_at, updated_at)
            VALUES (%s, %s, %s, %s, %s, %s)
        """, (
            order_card_id,
            f'[系统]分配给子拓收卡公司[{MOCK_CONFIG["buyer_company_id"]}]汇率:{int(rmb_rate)}',
            f'[System] assigned 子拓收卡公司[{MOCK_CONFIG["buyer_company_id"]}] rate:{int(rmb_rate)}',
            '127.0.0.1',
            t5, t5
        ))

        # 12. 插入 order_card_queues - 订单队列
        queue_id = random.randint(28477, 99999)
        cursor.execute("""
            INSERT INTO order_card_queues (order_card_id, company_id, admin_user_id, card_rate, rmb_card_rate,
                                         expired_at, checked_at, delayed_times, status, created_at, updated_at,
                                         auto_buy_provider, auto_buy_provider_status, card_id, country_id)
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        """, (
            order_card_id, MOCK_CONFIG['buyer_company_id'], MOCK_CONFIG['buyer_user_id'],
            rate, rmb_rate, t6 + timedelta(minutes=1), t6, 0, 2, t5, t7, 0, 0,
            MOCK_CONFIG['card_id'], MOCK_CONFIG['country_id']
        ))

        # 13. 插入 admin_operation_log - 收卡商接单
        cursor.execute("""
            INSERT INTO admin_operation_log (user_id, path, method, ip, input, created_at, updated_at)
            VALUES (%s, %s, %s, %s, %s, %s, %s)
        """, (
            MOCK_CONFIG['buyer_user_id'],
            'admin/company/buyer/user/check-card-ecode',
            'POST',
            '127.0.0.1',
            f'{{"order_card_id":"{order_card_id}","order_card_queue_id":"{queue_id}","local_time":"{t6.strftime("%Y-%m-%d %H:%M:%S")}"}}',
            t6, t6
        ))

        # 14. 插入 order_card_logs - 收卡商接单日志
        cursor.execute("""
            INSERT INTO order_card_logs (order_card_id, description, description_en, ip, created_at, updated_at)
            VALUES (%s, %s, %s, %s, %s, %s)
        """, (
            order_card_id,
            f'[系统]收卡商子拓收卡[{MOCK_CONFIG["buyer_user_id"]}]已接单：接单汇率{int(rmb_rate)}',
            f'[System]Card Collection 子拓收卡[{MOCK_CONFIG["buyer_user_id"]}]accepted the order with exchange rate{int(rmb_rate)}',
            '127.0.0.1',
            t6, t6
        ))

        # 15. 插入 admin_operation_log - 完成订单
        cursor.execute("""
            INSERT INTO admin_operation_log (user_id, path, method, ip, input, created_at, updated_at)
            VALUES (%s, %s, %s, %s, %s, %s, %s)
        """, (
            MOCK_CONFIG['buyer_user_id'],
            'admin/company/buyer/user/process-order-card-queue-done',
            'POST',
            '127.0.0.1',
            f'{{"order_card_queue_id":"{queue_id}","local_time":"{t7.strftime("%Y-%m-%d %H:%M:%S")}","company_update_card_info":"0","card_info":null}}',
            t7, t7
        ))

        # 16. 插入 order_card_logs - 完成订单日志
        cursor.execute("""
            INSERT INTO order_card_logs (order_card_id, description, description_en, ip, created_at, updated_at)
            VALUES (%s, %s, %s, %s, %s, %s)
        """, (
            order_card_id,
            f'订单已被收卡商子拓收卡[{MOCK_CONFIG["buyer_user_id"]}]手动完成',
            f'The order was manually fulfilled by Card Collection 子拓收卡[{MOCK_CONFIG["buyer_user_id"]}]',
            '127.0.0.1',
            t7, t7
        ))

        # 17. 插入 transactions - 出卡方交易
        seller_transaction_id = random.randint(266424, 999999)
        cursor.execute("""
            INSERT INTO transactions (id, company_id, rmb_amount, nr_amount, amount_currency, transaction_fee,
                                    exchange_rate, status, type, order_card_id, created_at, updated_at,
                                    owner_type, owner_id)
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        """, (
            seller_transaction_id, MOCK_CONFIG['seller_company_id'], profit, profit * 250, 1, commission,
            250, 1, 1, order_card_id, t3, t7, 'AdminUser', MOCK_CONFIG['seller_user_id']
        ))

        # 18. 插入 transactions - 收卡方交易
        buyer_transaction_id = seller_transaction_id + 1
        cursor.execute("""
            INSERT INTO transactions (id, company_id, rmb_amount, nr_amount, amount_currency, transaction_fee,
                                    exchange_rate, status, type, order_card_id, created_at, updated_at,
                                    owner_type, owner_id)
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        """, (
            buyer_transaction_id, MOCK_CONFIG['buyer_company_id'], balance * rmb_rate, balance * rmb_rate * 250, 1, 0,
            250, 1, 3, order_card_id, t6, t7, 'AdminUser', MOCK_CONFIG['buyer_user_id']
        ))

        # 19. 插入 transaction_records - 出卡方交易记录
        cursor.execute("""
            INSERT INTO transaction_records (company_id, transaction_id, rmb_amount, status, created_at, updated_at)
            VALUES (%s, %s, %s, %s, %s, %s)
        """, (
            MOCK_CONFIG['seller_company_id'], seller_transaction_id, profit, 0, t7, t7
        ))

        # 20. 插入 transaction_records - 收卡方交易记录
        cursor.execute("""
            INSERT INTO transaction_records (company_id, transaction_id, rmb_amount, status, created_at, updated_at)
            VALUES (%s, %s, %s, %s, %s, %s)
        """, (
            MOCK_CONFIG['buyer_company_id'], buyer_transaction_id, -(balance * rmb_rate), 0, t6, t6
        ))

        # 21. 插入 platform_transactions - 平台交易
        platform_transaction_id = random.randint(507241, 999999)
        cursor.execute("""
            INSERT INTO platform_transactions (id, transaction_id, company_id, owner_type, owner_id, balance,
                                             is_settlement, created_at, updated_at, type, transactionable_id,
                                             transactionable_type, status)
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        """, (
            platform_transaction_id, seller_transaction_id, MOCK_CONFIG['seller_company_id'],
            'AdminUser', MOCK_CONFIG['seller_user_id'], commission, 0, t7, t7, 3,
            seller_transaction_id, 'App\\Models\\Transaction', 1
        ))

        # 22. 插入 platform_transaction_records - 平台交易记录
        cursor.execute("""
            INSERT INTO platform_transaction_records (platform_transaction_id, rmb_amount, status, created_at, updated_at)
            VALUES (%s, %s, %s, %s, %s)
        """, (platform_transaction_id, commission, 0, t7, t7))

        # 23. 更新 company_card_profiles - 公司卡片配置（模拟累计金额增加）
        cursor.execute("""
            UPDATE company_card_profiles
            SET successful_rmb_amount = successful_rmb_amount + %s, updated_at = %s
            WHERE id = 42
        """, (balance * rmb_rate, t7))

        print(f"   ✅ 步骤9-10: 交易记录和平台数据完成 (平台交易ID: {platform_transaction_id})")
        print(f"   🎯 订单 {order_index + 1} 完整流程生成完成！")

    except Exception as e:
        print(f"   ❌ 插入数据时出错: {e}")
        raise

def main():
    """主函数"""
    print("🚀 开始生成模拟订单数据...")
    print(f"📊 配置信息:")
    print(f"   - 批量大小: {MOCK_CONFIG['batch_size']} 个订单")
    print(f"   - 时间间隔: {MOCK_CONFIG['time_interval_seconds']} 秒")
    print(f"   - 出卡用户: {MOCK_CONFIG['seller_user_id']}")
    print(f"   - 收卡用户: {MOCK_CONFIG['buyer_user_id']}")
    
    # 确认是否继续
    try:
        confirm = input("\n⚠️  确认要生成模拟数据吗？(输入 'yes' 继续): ")
        if confirm.lower() != 'yes':
            print("❌ 操作已取消")
            return
    except KeyboardInterrupt:
        print("\n❌ 操作已取消")
        return
    
    cnx = create_db_connection()
    cursor = cnx.cursor(dictionary=True)
    
    try:
        base_time = datetime.now()
        
        for i in range(MOCK_CONFIG['batch_size']):
            # 每个订单的基础时间
            order_base_time = base_time + timedelta(seconds=i * MOCK_CONFIG['time_interval_seconds'])
            
            # 插入订单数据
            insert_order_data(cursor, order_base_time, i)
            
            # 提交事务
            cnx.commit()
            
            print(f"   ✅ 第 {i + 1} 个订单数据生成完成")
            
            # 短暂延迟，避免数据库压力过大
            time.sleep(0.1)
        
        print(f"\n🎉 所有 {MOCK_CONFIG['batch_size']} 个订单数据生成完成！")
        
    except Exception as e:
        print(f"❌ 生成数据时出错: {e}")
        cnx.rollback()
    finally:
        cursor.close()
        cnx.close()

if __name__ == '__main__':
    main()
