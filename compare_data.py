import mysql.connector
import sys
from mysql.connector import errorcode

# --- 您需要修改的配置 ---

# 数据库连接信息
DB_CONFIG = {
    'user': 'root',
    'password': 'BwYvW64msQ5Lndu2Umpn',
    'host': 'nexus-master.cl4kksw821u9.ap-east-1.rds.amazonaws.com',
    'port': 3306,
    'database': 'staging_card' # 请再次确认这个数据库名完全正确
}

# 【新】监控表配置 (从列表改为字典)
# 格式: '表名': '主键名'
# 如果主键名是 None，脚本会尝试自动发现。
# 如果表没有主键，您可以在这里手动指定一个能保证唯一的列名。
TABLES_TO_MONITOR = {
    'admin_users': None,
    'orders': None,
    'order_statistics': None,
    'order_card_images': None,
    'admin_operation_log': None,
    'transaction_records': None,
    'transactions': None,
    'order_cards': None,
    'order_card_logs': None,
    'company_card_profiles': None,
    'platform_transaction_records': None,
    'platform_transactions': None,
    'order_card_queues': None
}

# -------------------------

def create_db_connection():
    try:
        if ':' in DB_CONFIG['host']:
            host, port_str = DB_CONFIG['host'].split(':')
            DB_CONFIG['host'] = host
            DB_CONFIG['port'] = int(port_str)
        cnx = mysql.connector.connect(**DB_CONFIG)
        print("✅ 数据库连接成功。")
        return cnx
    except mysql.connector.Error as err:
        if err.errno == errorcode.ER_ACCESS_DENIED_ERROR: print("❌ 数据库连接失败：用户名或密码错误。")
        elif err.errno == errorcode.ER_BAD_DB_ERROR: print(f"❌ 数据库连接失败：数据库 '{DB_CONFIG['database']}' 不存在。")
        else: print(f"❌ 数据库连接失败：{err}")
        sys.exit(1)

# *****【此函数已更新】*****
def get_primary_key(cursor, table_name):
    """自动获取指定表的主键列名，并增加调试信息"""
    try:
        # 1. 尝试精确查找名为 'PRIMARY' 的键
        cursor.execute(f"SHOW KEYS FROM `{table_name}` WHERE Key_name = 'PRIMARY'")
        keys = cursor.fetchall()
        
        if keys:
            # 找到了主键，正常返回
            pk_column = keys[0]['Column_name']
            print(f"  - 自动发现表 '{table_name}' 的主键为: '{pk_column}'")
            return pk_column
        else:
            # 2. 如果没找到，进入调试模式
            print(f"      ⚠️ 警告: 在表 '{table_name}' 中没有找到名为 'PRIMARY' 的键。")
            print("      🔍 正在检查该表上的所有索引...")
            cursor.execute(f"SHOW KEYS FROM `{table_name}`")
            all_keys = cursor.fetchall()

            if not all_keys:
                print(f"      📝 该表没有任何键或索引")
            else:
                print(f"      📝 该表的所有键/索引:")
                for key in all_keys:
                    key_type = "主键" if key['Key_name'] == 'PRIMARY' else "索引"
                    unique = "唯一" if key['Non_unique'] == 0 else "非唯一"
                    print(f"         - {key_type}: {key['Key_name']}, 列: {key['Column_name']}, {unique}")

            # 即使有其他键，但没有主键，对于脚本来说仍然无法保证唯一性，所以返回 None
            return None
            
    except Exception as e:
        print(f"❌ 获取表 '{table_name}' 主键时出错: {e}")
        return None

def fetch_table_data(cursor, table_name, pk_column):
    data_dict = {}
    try:
        cursor.execute(f"SELECT * FROM `{table_name}`")
        for row in cursor.fetchall():
            str_row = {k: str(v) for k, v in row.items()}
            data_dict[str_row[pk_column]] = str_row
    except Exception as e:
        print(f"❌ 查询表 '{table_name}' 数据时出错: {e}")
        return None
    return data_dict

def compare_and_print_diff(table_name, data_before, data_after, pk_column):
    print(f"\n==================== 正在对比表: {table_name} ====================")
    keys_before, keys_after = set(data_before.keys()), set(data_after.keys())
    added_keys, deleted_keys, common_keys = keys_after - keys_before, keys_before - keys_after, keys_before & keys_after
    has_changes = False
    if added_keys:
        has_changes = True
        print(f"---【新增了 {len(added_keys)} 行】---")
        for key in added_keys: print(f"  主键 '{pk_column}': {key}, 新增数据: {data_after[key]}")
    if deleted_keys:
        has_changes = True
        print(f"---【删除了 {len(deleted_keys)} 行】---")
        for key in deleted_keys: print(f"  主键 '{pk_column}': {key}, 被删数据: {data_before[key]}")
    modified_rows = []
    for key in common_keys:
        row_before, row_after = data_before[key], data_after[key]
        if row_before != row_after:
            changes = {}
            for field in row_before:
                if row_before[field] != row_after[field]: changes[field] = {'旧值': row_before[field], '新值': row_after[field]}
            if changes: modified_rows.append({'pk': key, 'changes': changes})
    if modified_rows:
        has_changes = True
        print(f"---【修改了 {len(modified_rows)} 行】---")
        for item in modified_rows:
            print(f"  主键 '{pk_column}': {item['pk']}")
            for field, values in item['changes'].items(): print(f"    - 字段 '{field}': 从 '{values['旧值']}' -> 变为 '{values['新值']}'")
    if not has_changes: print("数据完全一致，未发现任何变化。")
    print(f"==================== 表 {table_name} 对比结束 ====================\n")

def main():
    print("--- 第一步：正在获取操作前的数据快照... ---")
    cnx_before = create_db_connection()
    cursor_before = cnx_before.cursor(dictionary=True)

    # 首先检查数据库中实际存在的表
    print("\n🔍 正在检查数据库中的所有表...")
    cursor_before.execute("SHOW TABLES")
    existing_tables = [row[f'Tables_in_{DB_CONFIG["database"]}'] for row in cursor_before.fetchall()]
    print(f"📋 数据库 '{DB_CONFIG['database']}' 中共有 {len(existing_tables)} 个表:")
    for table in existing_tables:
        print(f"   - {table}")

    snapshot_data = {}
    processed_count = 0
    skipped_count = 0

    print(f"\n📊 开始处理配置中的 {len(TABLES_TO_MONITOR)} 个表...")

    for table, manual_pk in TABLES_TO_MONITOR.items():
        print(f"\n🔄 正在处理表: '{table}'")

        # 检查表是否存在
        if table not in existing_tables:
            print(f"   ❌ 跳过: 表 '{table}' 在数据库中不存在")
            skipped_count += 1
            continue

        pk = manual_pk
        if pk is None:
            print(f"   🔍 正在自动检测表 '{table}' 的主键...")
            pk = get_primary_key(cursor_before, table)
        else:
            print(f"   ✏️ 手动指定表 '{table}' 的唯一键为: '{pk}'")

        if pk:
            print(f"   📥 正在获取表 '{table}' 的数据...")
            data = fetch_table_data(cursor_before, table, pk)
            if data is not None:
                snapshot_data[table] = {'pk': pk, 'data': data}
                print(f"   ✅ 成功: 表 '{table}' 已加入监控 (共 {len(data)} 行数据)")
                processed_count += 1
            else:
                print(f"   ❌ 跳过: 表 '{table}' 数据获取失败")
                skipped_count += 1
        else:
            print(f"   ❌ 跳过: 表 '{table}' 无法确定主键")
            skipped_count += 1

    cursor_before.close()
    cnx_before.close()

    print(f"\n📈 处理结果统计:")
    print(f"   ✅ 成功处理: {processed_count} 个表")
    print(f"   ❌ 跳过: {skipped_count} 个表")
    print(f"   📊 总计: {processed_count + skipped_count} 个表")
    print("✅ 操作前数据快照已保存在内存中。")
    try: input("\n>>> 请现在去执行您的业务操作，完成后回到这里，按 Enter键 继续进行对比...")
    except KeyboardInterrupt: print("\n用户中断操作，程序退出。"); sys.exit(0)
    print("\n--- 第二步：正在获取操作后的数据并进行对比... ---")
    cnx_after = create_db_connection()
    cursor_after = cnx_after.cursor(dictionary=True)
    for table, table_snapshot in snapshot_data.items():
        pk, data_before = table_snapshot['pk'], table_snapshot['data']
        data_after = fetch_table_data(cursor_after, table, pk)
        if data_after is not None: compare_and_print_diff(table, data_before, data_after, pk)
    cursor_after.close()
    cnx_after.close()
    print("🎉 所有表对比完成。")

if __name__ == '__main__':
    main()