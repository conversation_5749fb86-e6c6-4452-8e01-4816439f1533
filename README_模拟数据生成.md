# 模拟数据生成系统

## 📋 项目概述

基于真实业务流程的时间关系分析，创建了一个完整的模拟订单数据生成系统。该系统能够按照真实的业务逻辑和时间顺序，批量生成模拟的订单数据。

## 🎯 功能特点

### 1. 数据变更监控 (`compare_data.py`)
- ✅ 监控13个核心业务表的数据变化
- ✅ 详细的调试信息和处理状态显示
- ✅ 自动检测表结构和主键
- ✅ 实时对比数据变化

### 2. 模拟数据生成 (`generate_mock_data.py`)
- ✅ 基于真实业务流程的时间关系
- ✅ 完整的订单生命周期模拟
- ✅ 支持批量生成和自定义配置
- ✅ 事务安全和错误处理

### 3. 数据验证 (`verify_mock_data.py`)
- ✅ 验证生成数据的完整性
- ✅ 统计各表的新增记录数量
- ✅ 显示最新订单详情

## 📊 业务流程时间关系分析

根据真实数据变更日志分析，订单完整流程的时间关系如下：

```
t1 (基础时间)     : 查询汇率
t2 (t1 + 3秒)    : 上传图片
t3 (t2 + 1秒)    : 创建订单
t4 (t3 + 3秒)    : 访问页面
t5 (t4 + 1秒)    : 系统分配
t6 (t5 + 3秒)    : 收卡商接单
t7 (t6 + 1秒)    : 完成订单

总耗时: 约12秒完成一个完整订单流程
```

## 🗃️ 涉及的数据表

### 核心业务表 (13个)
1. **admin_users** - 管理员用户表
2. **orders** - 订单主表
3. **order_statistics** - 订单统计表
4. **order_card_images** - 订单卡片图片表
5. **admin_operation_log** - 管理员操作日志表
6. **transaction_records** - 交易记录表
7. **transactions** - 交易主表
8. **order_cards** - 订单卡片表
9. **order_card_logs** - 订单卡片日志表
10. **company_card_profiles** - 公司卡片配置表
11. **platform_transaction_records** - 平台交易记录表
12. **platform_transactions** - 平台交易表
13. **order_card_queues** - 订单卡片队列表

## 🚀 使用方法

### 1. 运行数据监控
```bash
python compare_data.py
```
- 获取操作前数据快照
- 执行业务操作
- 按Enter键进行数据对比

### 2. 生成模拟数据
```bash
python generate_mock_data.py
```
- 配置生成参数（批量大小、时间间隔等）
- 输入 'yes' 确认生成
- 自动生成完整的订单流程数据

### 3. 验证生成结果
```bash
python verify_mock_data.py
```
- 检查最近10分钟的新增数据
- 统计各表记录数量
- 显示最新订单详情

## ⚙️ 配置参数

### 模拟数据配置 (`MOCK_CONFIG`)
```python
MOCK_CONFIG = {
    'batch_size': 5,              # 每批生成的订单数量
    'time_interval_seconds': 10,  # 每个订单之间的时间间隔（秒）
    'seller_user_id': 121,        # 出卡用户ID
    'buyer_user_id': 120,         # 收卡用户ID
    'seller_company_id': 38,      # 出卡公司ID
    'buyer_company_id': 37,       # 收卡公司ID
    'card_id': 47,                # 卡片ID (Steam US)
    'country_id': 1,              # 国家ID (美国)
}
```

## 📈 验证结果示例

最近一次生成的数据统计：
- 📊 orders 表: 6 条新记录
- 📊 order_cards 表: 6 条新记录
- 📊 order_statistics 表: 6 条新记录
- 📊 order_card_images 表: 5 条新记录
- 📊 admin_operation_log 表: 49 条新记录
- 📊 transactions 表: 15 条新记录
- 📊 transaction_records 表: 11 条新记录
- 📊 order_card_logs 表: 25 条新记录
- 📊 order_card_queues 表: 6 条新记录
- 📊 platform_transactions 表: 12 条新记录
- 📊 platform_transaction_records 表: 12 条新记录

## 🔧 技术特点

### 1. 时间精确性
- 严格按照真实业务流程的时间间隔
- 支持自定义时间间隔配置
- 确保数据的时间逻辑一致性

### 2. 数据完整性
- 涵盖订单完整生命周期的所有相关表
- 保持数据间的关联关系
- 模拟真实的业务数据结构

### 3. 可扩展性
- 支持批量生成任意数量的订单
- 可配置的业务参数
- 易于修改和扩展新的业务场景

### 4. 安全性
- 事务安全，支持回滚
- 详细的错误处理和日志
- 数据验证和完整性检查

## 📝 注意事项

1. **数据库权限**: 确保有足够的数据库读写权限
2. **时间同步**: 注意服务器时间与本地时间的差异
3. **数据量控制**: 建议先用小批量测试，再进行大批量生成
4. **备份策略**: 重要环境建议先备份数据库
5. **监控影响**: 大量数据生成可能影响数据库性能

## 🎉 总结

该模拟数据生成系统成功实现了：
- ✅ 完整的业务流程模拟
- ✅ 精确的时间关系控制
- ✅ 全面的数据表覆盖
- ✅ 可靠的数据验证机制

系统可以用于测试、开发、性能测试等多种场景，为业务系统提供高质量的模拟数据支持。
