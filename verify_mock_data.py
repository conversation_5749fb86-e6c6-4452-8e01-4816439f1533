#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证模拟数据脚本
检查最近生成的模拟数据是否成功插入到数据库中
"""

import mysql.connector
import sys
from datetime import datetime, timedelta
from mysql.connector import errorcode

# 数据库连接配置
DB_CONFIG = {
    'user': 'root',
    'password': 'BwYvW64msQ5Lndu2Umpn',
    'host': 'nexus-master.cl4kksw821u9.ap-east-1.rds.amazonaws.com',
    'port': 3306,
    'database': 'staging_card'
}

def create_db_connection():
    """创建数据库连接"""
    try:
        cnx = mysql.connector.connect(**DB_CONFIG)
        print("✅ 数据库连接成功")
        return cnx
    except mysql.connector.Error as err:
        if err.errno == errorcode.ER_ACCESS_DENIED_ERROR:
            print("❌ 数据库连接失败：用户名或密码错误")
        elif err.errno == errorcode.ER_BAD_DB_ERROR:
            print(f"❌ 数据库连接失败：数据库 '{DB_CONFIG['database']}' 不存在")
        else:
            print(f"❌ 数据库连接失败：{err}")
        sys.exit(1)

def check_recent_data():
    """检查最近生成的数据"""
    cnx = create_db_connection()
    cursor = cnx.cursor(dictionary=True)
    
    # 检查最近10分钟的数据
    time_threshold = datetime.now() - timedelta(minutes=10)
    time_str = time_threshold.strftime('%Y-%m-%d %H:%M:%S')
    
    print(f"🔍 检查 {time_str} 之后的数据...")
    
    try:
        # 1. 检查 orders 表
        cursor.execute("""
            SELECT COUNT(*) as count, MAX(created_at) as latest_time 
            FROM orders 
            WHERE created_at >= %s
        """, (time_str,))
        result = cursor.fetchone()
        print(f"📊 orders 表: {result['count']} 条新记录, 最新时间: {result['latest_time']}")
        
        # 2. 检查 order_cards 表
        cursor.execute("""
            SELECT COUNT(*) as count, MAX(created_at) as latest_time 
            FROM order_cards 
            WHERE created_at >= %s
        """, (time_str,))
        result = cursor.fetchone()
        print(f"📊 order_cards 表: {result['count']} 条新记录, 最新时间: {result['latest_time']}")
        
        # 3. 检查 order_statistics 表
        cursor.execute("""
            SELECT COUNT(*) as count, MAX(created_at) as latest_time 
            FROM order_statistics 
            WHERE created_at >= %s
        """, (time_str,))
        result = cursor.fetchone()
        print(f"📊 order_statistics 表: {result['count']} 条新记录, 最新时间: {result['latest_time']}")
        
        # 4. 检查 order_card_images 表
        cursor.execute("""
            SELECT COUNT(*) as count, MAX(created_at) as latest_time 
            FROM order_card_images 
            WHERE created_at >= %s
        """, (time_str,))
        result = cursor.fetchone()
        print(f"📊 order_card_images 表: {result['count']} 条新记录, 最新时间: {result['latest_time']}")
        
        # 5. 检查 admin_operation_log 表
        cursor.execute("""
            SELECT COUNT(*) as count, MAX(created_at) as latest_time 
            FROM admin_operation_log 
            WHERE created_at >= %s
        """, (time_str,))
        result = cursor.fetchone()
        print(f"📊 admin_operation_log 表: {result['count']} 条新记录, 最新时间: {result['latest_time']}")
        
        # 6. 检查 transactions 表
        cursor.execute("""
            SELECT COUNT(*) as count, MAX(created_at) as latest_time 
            FROM transactions 
            WHERE created_at >= %s
        """, (time_str,))
        result = cursor.fetchone()
        print(f"📊 transactions 表: {result['count']} 条新记录, 最新时间: {result['latest_time']}")
        
        # 7. 检查 transaction_records 表
        cursor.execute("""
            SELECT COUNT(*) as count, MAX(created_at) as latest_time 
            FROM transaction_records 
            WHERE created_at >= %s
        """, (time_str,))
        result = cursor.fetchone()
        print(f"📊 transaction_records 表: {result['count']} 条新记录, 最新时间: {result['latest_time']}")
        
        # 8. 检查 order_card_logs 表
        cursor.execute("""
            SELECT COUNT(*) as count, MAX(created_at) as latest_time 
            FROM order_card_logs 
            WHERE created_at >= %s
        """, (time_str,))
        result = cursor.fetchone()
        print(f"📊 order_card_logs 表: {result['count']} 条新记录, 最新时间: {result['latest_time']}")
        
        # 9. 检查 order_card_queues 表
        cursor.execute("""
            SELECT COUNT(*) as count, MAX(created_at) as latest_time 
            FROM order_card_queues 
            WHERE created_at >= %s
        """, (time_str,))
        result = cursor.fetchone()
        print(f"📊 order_card_queues 表: {result['count']} 条新记录, 最新时间: {result['latest_time']}")
        
        # 10. 检查 platform_transactions 表
        cursor.execute("""
            SELECT COUNT(*) as count, MAX(created_at) as latest_time 
            FROM platform_transactions 
            WHERE created_at >= %s
        """, (time_str,))
        result = cursor.fetchone()
        print(f"📊 platform_transactions 表: {result['count']} 条新记录, 最新时间: {result['latest_time']}")
        
        # 11. 检查 platform_transaction_records 表
        cursor.execute("""
            SELECT COUNT(*) as count, MAX(created_at) as latest_time 
            FROM platform_transaction_records 
            WHERE created_at >= %s
        """, (time_str,))
        result = cursor.fetchone()
        print(f"📊 platform_transaction_records 表: {result['count']} 条新记录, 最新时间: {result['latest_time']}")
        
        print("\n🎯 检查完成！")
        
        # 显示最新的几个订单详情
        print("\n📋 最新的5个订单详情:")
        cursor.execute("""
            SELECT id, company_id, owner_id, status, amount_profit_rmb, amount_performance_rmb, 
                   created_at, updated_at
            FROM orders 
            WHERE created_at >= %s
            ORDER BY created_at DESC 
            LIMIT 5
        """, (time_str,))
        
        orders = cursor.fetchall()
        for order in orders:
            print(f"   订单ID: {order['id']}, 公司: {order['company_id']}, "
                  f"利润: {order['amount_profit_rmb']}, 业绩: {order['amount_performance_rmb']}, "
                  f"创建时间: {order['created_at']}")
        
    except Exception as e:
        print(f"❌ 查询数据时出错: {e}")
    finally:
        cursor.close()
        cnx.close()

def main():
    """主函数"""
    print("🔍 开始验证模拟数据...")
    check_recent_data()

if __name__ == '__main__':
    main()
